import { NextResponse } from 'next/server'
import { verifyAuth } from '@/utils/auth'
import { serverCache } from '@/lib/cache'
import { queryOne } from '@/lib/postgres'

export async function GET() {
  try {
    // Verify authentication
    const { authenticated, authId } = await verifyAuth()
    if (!authenticated || !authId) {
      return NextResponse.json({ 
        success: false,
        body: null,
        error_msg: 'Unauthorized'
      }, { status: 401 })
    }

    // Prepare SQL query for dashboard data
    const sql = `
      SELECT
        c.client_id,
        c.username,
        c.sector,
        c.lang,
        c.plan_type,
        c.next_billing_date,
        c.usage_used,
        c.usage_limit,
        COALESCE(p.total_faqs, 0) as faq_limit,
        COALESCE(p.total_photos, 0) as photo_limit,
        (SELECT COUNT(*) FROM faqs WHERE client_id = c.client_id AND is_visible = true) as faq_count,
        (SELECT COUNT(*) FROM photos WHERE client_id = c.client_id) as photo_count
      FROM clients c
      LEFT JOIN plans p ON p.name = c.plan_type
      WHERE c.auth_id = $1
    `

    // Execute direct PostgreSQL query
    const row = await queryOne(sql, [authId])

    if (!row || !row.client_id) {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: 'No dashboard data found for user'
      }, { status: 404 })
    }

    // Now that we have client_id, check cache first
    const clientId = row.client_id
    const cacheKey = `dashboard_${clientId}`
    const cachedResult = serverCache.get(cacheKey)
    if (cachedResult) {
      return NextResponse.json({
        success: true,
        body: cachedResult,
        error_msg: null,
        cached: true
      })
    }


    const faqUsagePercentage = row.faq_limit > 0 ? Math.min((row.faq_count / row.faq_limit) * 100, 100) : 0
    const photoUsagePercentage = row.photo_limit > 0 ? Math.min((row.photo_count / row.photo_limit) * 100, 100) : 0

    const dashboardData = {
      clientInfo: {
        username: row.username,
        sector: row.sector,
        lang: row.lang,
        plan_type: row.plan_type,
        next_billing_date: row.next_billing_date
      },
      usageData: {
        usage_used: row.usage_used || 0,
        usage_limit: row.usage_limit || 100
      },
      knowledgeStats: {
        faqCount: row.faq_count || 0,
        photoCount: row.photo_count || 0,
        faqLimit: row.faq_limit || 0,
        photoLimit: row.photo_limit || 0,
        faqUsagePercentage: Math.round(faqUsagePercentage),
        photoUsagePercentage: Math.round(photoUsagePercentage)
      }
    }

    // Cache the result for 30 minutes
    serverCache.set(cacheKey, dashboardData, 30)

    return NextResponse.json({
      success: true,
      body: dashboardData,
      error_msg: null
    })
  } catch (error) {
    console.error('Error in dashboard data API:', error)
    return NextResponse.json(
      { 
        success: false,
        body: null,
        error_msg: error instanceof Error ? error.message : 'Internal server error'
      },
      { status: 500 }
    )
  }
}