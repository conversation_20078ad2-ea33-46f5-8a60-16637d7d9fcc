import { NextResponse } from 'next/server'
import { verifyAuth } from '@/utils/auth'
import { generateWebhookToken } from '@/utils/jwt'
import { nanoid } from 'nanoid'
import { v4 as uuidv4 } from 'uuid'
import { getM4AExtension } from '@/utils/audioConversion'
import { serverCache } from '@/lib/cache'
import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3'
import { transaction } from '@/lib/postgres'

// Validate R2 environment variables
const validateR2Config = () => {
  const requiredEnvVars = [
    'CLOUDFLARE_R2_ENDPOINT',
    'CLOUDFLARE_R2_ACCESS_KEY_ID',
    'CLOUDFLARE_R2_SECRET_ACCESS_KEY',
    'CLOUDFLARE_R2_BUCKET_NAME',
    'CLOUDFLARE_R2_PUBLIC_URL'
  ]
  
  for (const envVar of requiredEnvVars) {
    if (!process.env[envVar]) {
      throw new Error(`Missing required environment variable: ${envVar}`)
    }
  }
}

// Validate configuration on module load
validateR2Config()

// Initialize R2 client with proper configuration for Cloudflare R2
const r2Client = new S3Client({
  region: 'auto', // R2 uses 'auto' region
  endpoint: process.env.CLOUDFLARE_R2_ENDPOINT!,
  forcePathStyle: true, // Required for R2 compatibility
  credentials: {
    accessKeyId: process.env.CLOUDFLARE_R2_ACCESS_KEY_ID!,
    secretAccessKey: process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY!,
  },
})

const BUCKET_NAME = process.env.CLOUDFLARE_R2_BUCKET_NAME!
const PUBLIC_URL = process.env.CLOUDFLARE_R2_PUBLIC_URL!

// Helper function to determine content type from file extension
const getContentType = (fileName: string): string => {
  const ext = fileName.toLowerCase().split('.').pop()
  switch (ext) {
    case 'm4a':
      return 'audio/mp4'
    case 'mp3':
      return 'audio/mpeg'
    case 'mp4':
      return 'audio/mp4'
    case 'webm':
      return 'audio/webm'
    case 'ogg':
      return 'audio/ogg'
    case 'wav':
      return 'audio/wav'
    case 'aac':
      return 'audio/aac'
    default:
      return 'audio/mp4' // Default to mp4 for M4A files
  }
}

export async function POST(request: Request) {
  try {
    // Verify authentication
    const { authenticated, authId, clientId } = await verifyAuth()
    if (!authenticated || !authId) {
      return NextResponse.json({ 
        success: false,
        body: null,
        error_msg: 'Unauthorized'
      }, { status: 401 })
    }

    // Validate clientId exists
    if (!clientId) {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: 'Client ID not found in authentication'
      }, { status: 400 })
    }

    // Get the secure webhook URL from environment
    const webhookUrl = process.env.CHHLAT_DB_WEBHOOK_URL
    
    if (!webhookUrl) {
      console.warn('Secure webhook URL not configured')
      return NextResponse.json({ 
        success: false,
        body: null,
        error_msg: 'Database webhook not configured'
      }, { status: 500 })
    }

    // Parse FormData request
    const formData = await request.formData()
    const faqBatchString = formData.get('faqBatch') as string
    
    if (!faqBatchString) {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: 'Missing required field: faqBatch'
      }, { status: 400 })
    }

    const faqBatch = JSON.parse(faqBatchString)

    // Validate required fields
    if (!faqBatch || !Array.isArray(faqBatch) || faqBatch.length === 0) {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: 'Missing required field: faqBatch'
      }, { status: 400 })
    }


    // Validate batch size (max 20 FAQs)
    const MAX_BATCH_SIZE = 10
    if (faqBatch.length > MAX_BATCH_SIZE) {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: `Batch size ${faqBatch.length} exceeds limit of ${MAX_BATCH_SIZE}`
      }, { status: 400 })
    }

    // Validate each FAQ in the batch
    for (let i = 0; i < faqBatch.length; i++) {
      const faq = faqBatch[i]
      
      if (!faq.question || typeof faq.question !== 'string' || faq.question.trim() === '') {
        return NextResponse.json({
          success: false,
          body: null,
          error_msg: `FAQ ${i + 1}: Question is required`
        }, { status: 400 })
      }

      if (!faq.isAudioAnswer && (!faq.answer || typeof faq.answer !== 'string' || faq.answer.trim() === '')) {
        return NextResponse.json({
          success: false,
          body: null,
          error_msg: `FAQ ${i + 1}: Answer is required for text FAQs`
        }, { status: 400 })
      }

      if (faq.isAudioAnswer && (!faq.answer || typeof faq.answer !== 'string' || faq.answer.trim() === '')) {
        return NextResponse.json({
          success: false,
          body: null,
          error_msg: `FAQ ${i + 1}: Audio code is required for audio FAQs`
        }, { status: 400 })
      }
      
      // Validate audio blob exists for audio FAQs
      if (faq.isAudioAnswer && faq.audioIndex !== undefined && faq.audioIndex !== null) {
        const audioBlob = formData.get(`audioBlob_${faq.audioIndex}`)
        if (!audioBlob) {
          return NextResponse.json({
            success: false,
            body: null,
            error_msg: `FAQ ${i + 1}: Missing processed audio blob for audio FAQ`
          }, { status: 400 })
        }
      }
    }

    // Generate batch ID 
    const batchId = nanoid(12)
    
    // Step 1: Split FAQs by type
    const textFaqs = faqBatch.filter(faq => !faq.isAudioAnswer)
    const audioFaqs = faqBatch.filter(faq => faq.isAudioAnswer)
    
    
    // Step 2: Process audio FAQs with direct parallel R2 upload (no chunking)
    const processedAudioFaqs: Array<{
      faq_id: string
      question: string
      answer: string
      audio_url: string | null
      audio_duration: number | null
      audio_file_path: string | null
      photo_id: string | null
      photo_urls: string[] | null
      is_audio: boolean
      success: boolean
      error?: string
    }> = []
    
    if (audioFaqs.length > 0) {
      // Process ALL audio FAQs in parallel - no chunking
      const allAudioResults = await Promise.allSettled(
        audioFaqs.map(async (faq) => {
          try {
            // Get pre-processed audio blob from FormData
            const audioBlob = (faq.audioIndex !== undefined && faq.audioIndex !== null) ? formData.get(`audioBlob_${faq.audioIndex}`) as File : null
            
            if (!audioBlob) {
              throw new Error('Missing processed audio blob for audio FAQ')
            }
            
            // Generate audio ID and file path with proper UUID
            const audioCode = faq.answer // Use the audio code as the base
            const uniqueId = uuidv4()
            const audioId = `${audioCode}-${uniqueId}`
            const fileExtension = getM4AExtension()
            const fileName = `${audioId}.${fileExtension}`
            const filePath = `audios/${authId}/${fileName}`
            
            // Convert blob to buffer for direct R2 upload
            const audioBuffer = Buffer.from(await audioBlob.arrayBuffer())
            const contentType = getContentType(fileName)
            
            // Direct R2 upload - no HTTP overhead
            const putCommand = new PutObjectCommand({
              Bucket: BUCKET_NAME,
              Key: filePath,
              Body: new Uint8Array(audioBuffer),
              ContentType: contentType,
              CacheControl: '3600',
              ContentDisposition: `inline; filename="${fileName}"`,
            })
            
            await r2Client.send(putCommand)
            
            // Generate public URL
            const publicUrl = `${PUBLIC_URL}/${filePath}`
            
            // Return processed FAQ with audio URL
            return {
              faq_id: nanoid(16),
              question: faq.question,
              answer: '', // Empty for audio FAQs
              audio_url: publicUrl,
              audio_duration: faq.audioDuration || null,
              audio_file_path: filePath,
              photo_id: faq.photoInfo?.photo_id || null,
              photo_urls: faq.photoInfo?.full_photo_urls || null,
              is_audio: true,
              success: true
            }
            
          } catch (error) {
            console.error(`Error processing audio FAQ "${faq.question}":`, error)
            return {
              faq_id: nanoid(16),
              question: faq.question,
              answer: '',
              audio_url: null,
              audio_duration: faq.audioDuration || null,
              audio_file_path: null,
              photo_id: faq.photoInfo?.photo_id || null,
              photo_urls: faq.photoInfo?.full_photo_urls || null,
              is_audio: true,
              success: false,
              error: error instanceof Error ? error.message : 'Unknown error'
            }
          }
        })
      )
      
      // Process results from Promise.allSettled
      processedAudioFaqs.push(...allAudioResults.map(result => 
        result.status === 'fulfilled' ? result.value : result.reason
      ))
    }
    
    // Step 3: Prepare text FAQs (no processing needed)
    const processedTextFaqs = textFaqs.map(faq => ({
      faq_id: nanoid(16),
      question: faq.question,
      answer: faq.answer,
      audio_url: null,
      audio_duration: null,
      audio_file_path: null,
      photo_id: faq.photoInfo?.photo_id || null,
      photo_urls: faq.photoInfo?.full_photo_urls || null,
      is_audio: false,
      success: true,
      error: undefined
    }))
    
    // Step 4: Combine all processed FAQs
    const allProcessedFaqs = [...processedTextFaqs, ...processedAudioFaqs]
    const successfulFaqs = allProcessedFaqs.filter(faq => faq.success)
    const failedFaqs = allProcessedFaqs.filter(faq => !faq.success)
    

    // Generate JWT token for webhook authentication
    const jwtToken = generateWebhookToken()

    // Step 5: Get client info for webhook payload from knowledge cache
    let clientInfo = null
    try {
      const knowledgeCacheKey = `knowledge_${authId}`
      const knowledgeData = serverCache.get(knowledgeCacheKey)
      if (knowledgeData) {
        clientInfo = {
          sector: knowledgeData.sector,
          lang: knowledgeData.clientLang
        }
      }
    } catch (error) {
      console.warn('Could not get client info from cache:', error)
    }

    // Step 6: Insert successful FAQs directly to PostgreSQL
    let insertedFaqs = []
    if (successfulFaqs.length > 0) {
      try {
        // Use transaction to ensure all FAQs are inserted atomically
        insertedFaqs = await transaction(async (client) => {
          const insertedResults: any[] = []

          for (const faq of successfulFaqs) {
            const insertSql = `
              INSERT INTO faqs (
                client_id, faq_id, question, answer, audio_url,
                audio_duration, audio_file_path, photo_id, photo_url, is_visible
              ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
              RETURNING id, faq_id, created_at
            `

            const result = await client.query(insertSql, [
              clientId,
              faq.faq_id,
              faq.question,
              faq.answer,
              faq.audio_url,
              faq.audio_duration,
              faq.audio_file_path,
              faq.photo_id,
              faq.photo_urls, // This will be stored as array in photo_url column
              true // is_visible = true for new FAQs
            ])

            insertedResults.push(result.rows[0])
          }

          return insertedResults
        })

        console.log(`Successfully inserted ${insertedFaqs.length} FAQs to PostgreSQL`)

      } catch (error) {
        console.error('Error inserting FAQs to PostgreSQL:', error)
        throw new Error(`Failed to save FAQs to database: ${error instanceof Error ? error.message : 'Unknown error'}`)
      }

      // Step 6b: Send to webhook as fire-and-forget (for other purposes like analytics, etc.)
      // Don't await this - let it run in background
      if (webhookUrl) {
        fetch(webhookUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${jwtToken}`
          },
          body: JSON.stringify({
            mode: 'faq',
            operation: 'add_batch',
            batch_id: batchId,
            batch_size: successfulFaqs.length,
            faqs: successfulFaqs.map(faq => ({
              conversation_id: uuidv4(), // Generate UUID for each FAQ
              faq_id: faq.faq_id,
              client_id: clientId,
              question: faq.question,
              answer: faq.answer,
              sector: clientInfo?.sector || null,
              lang: clientInfo?.lang || null,
              audio_url: faq.audio_url
            }))
          })
        }).catch(error => {
          // Log webhook errors but don't fail the request
          console.warn('Fire-and-forget webhook failed:', error)
        })
      }
    }

    // Step 7: Update knowledge cache with new FAQ count (if cache exists)
    try {
      const knowledgeCacheKey = `knowledge_${authId}`
      const knowledgeData = serverCache.get(knowledgeCacheKey)
      
      if (knowledgeData) {
        // Update FAQ count in cache
        const currentFaqCount = knowledgeData.knowledgeStats.faqCount || 0
        const newFaqCount = currentFaqCount + successfulFaqs.length
        const faqLimit = knowledgeData.knowledgeStats.faqLimit || 1
        
        knowledgeData.knowledgeStats.faqCount = newFaqCount
        knowledgeData.knowledgeStats.faqUsagePercentage = Math.round((newFaqCount / faqLimit) * 100)
        
        serverCache.set(knowledgeCacheKey, knowledgeData, 30)
      } else {
      }
    } catch (error) {
      console.warn('Could not update knowledge cache:', error)
    }

    // Step 8: Return combined results
    return NextResponse.json({
      success: true,
      body: {
        items_processed: successfulFaqs.length,
        items_failed: failedFaqs.length,
        total_items: allProcessedFaqs.length,
        batch_id: batchId,
        failures: failedFaqs.map(faq => ({
          question: faq.question,
          error: faq.error
        })),
        inserted_faqs: insertedFaqs || null
      },
      error_msg: failedFaqs.length > 0 ? `${failedFaqs.length} items failed to process` : null
    })

  } catch (error: unknown) {
    console.error('Error in FAQ batch add API:', error)
    return NextResponse.json(
      { 
        success: false,
        body: null,
        error_msg: error instanceof Error ? error.message : 'Internal server error'
      },
      { status: 500 }
    )
  }
}