import { NextResponse } from 'next/server'
import { verifyAuth } from '@/utils/auth'
import { queryOne } from '@/lib/postgres'

export async function POST(request: Request) {
  try {
    // Verify authentication
    const { authenticated, authId } = await verifyAuth()
    if (!authenticated || !authId) {
      return NextResponse.json({ 
        success: false,
        body: null,
        error_msg: 'Unauthorized'
      }, { status: 401 })
    }

    // Execute direct PostgreSQL query to fetch photos
    const sql = `
      SELECT COALESCE(
        JSON_AGG(
          JSON_BUILD_OBJECT(
            'id', id,
            'photo_id', photo_id,
            'photo_url', photo_url,
            'photo_file_path', photo_file_path,
            'updated_at', updated_at
          ) ORDER BY updated_at DESC
        ),
        '[]'::json
      ) as photos
      FROM photos
      WHERE client_id = (SELECT client_id FROM clients WHERE auth_id = $1)
    `

    const result = await queryOne(sql, [authId])

    if (!result) {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: 'No photos found for user'
      }, { status: 404 })
    }
    
    // Extract photos from the result (should be the photos JSON array)
    const photos = result?.photos || []

    // Return standardized response format
    return NextResponse.json({
      success: true,
      body: photos,
      error_msg: null
    })

  } catch (error: unknown) {
    console.error('Error in photo search API:', error)
    return NextResponse.json(
      { 
        success: false,
        body: null,
        error_msg: error instanceof Error ? error.message : 'Internal server error'
      },
      { status: 500 }
    )
  }
}