


amazing thank you

now for @/Users/<USER>/Documents/chhlat-bot/src/app/dashboard/knowledge/page.tsx , we also need this migration. Currently, it might still use the webhook, but we need to migrate the sql and params part to directly insert to postgres, where we still keep the other body data to the webhook but in a form of fire and forget (no need to wait when trigger the webhok since we update info to postgres alr, those data will be use for other purposes only)