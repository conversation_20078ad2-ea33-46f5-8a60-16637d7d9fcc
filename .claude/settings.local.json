{"permissions": {"allow": ["Bash(rg:*)", "Bash(grep:*)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -A 50 -B 5 \"showDeleteConfirmation|showAudioDeleteConfirmation\" /Users/<USER>/Documents/chhlat-bot/src/app/dashboard/knowledge/ --type tsx)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -A 50 -B 5 \"showDeleteConfirmation|showAudioDeleteConfirmation\" /Users/<USER>/Documents/chhlat-bot/src/app/dashboard/knowledge/)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -A 30 -B 2 \"Delete Confirmation Modal|Audio Delete Confirmation Modal\" /Users/<USER>/Documents/chhlat-bot/src/app/dashboard/knowledge/page.tsx)", "Bash(git add:*)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n \"Image Gallery Modal|imageGallery\" /Users/<USER>/Documents/chhlat-bot/src/app/dashboard/knowledge/photo/page.tsx)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n \"useTheme|ThemeContext|themeConfig\" /Users/<USER>/Documents/chhlat-bot/src/app/dashboard/knowledge/photo/page.tsx)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n \"Image Gallery Modal|imageGallery\" /Users/<USER>/Documents/chhlat-bot/src/app/dashboard/knowledge/page.tsx)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n \"useTheme|ThemeContext|themeConfig\" /Users/<USER>/Documents/chhlat-bot/src/app/dashboard/knowledge/page.tsx)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n \"const.*theme.*=\" /Users/<USER>/Documents/chhlat-bot/src/app/dashboard/knowledge/page.tsx)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n \"Image Gallery Modal|imageGallery\" /Users/<USER>/Documents/chhlat-bot/src/app/dashboard/knowledge/knowledgeBase/page.tsx)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n \"useTheme|ThemeContext|themeConfig\" /Users/<USER>/Documents/chhlat-bot/src/app/dashboard/knowledge/knowledgeBase/page.tsx)", "Bash(git commit:*)", "Bash(npm run dev:*)", "Bash(npm run lint)", "Bash(npx tsc:*)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -A 20 -B 5 \"startRecording|stopRecording\" \"/Users/<USER>/Documents/chhlat-bot/src/app/dashboard/knowledge/page.tsx\")", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -A 10 -B 5 \"MediaRecorder|getUserMedia\" \"/Users/<USER>/Documents/chhlat-bot/src/app/dashboard/knowledge/page.tsx\")", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -A 15 \"onstop.*=\" \"/Users/<USER>/Documents/chhlat-bot/src/app/dashboard/knowledge/page.tsx\")", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -A 5 -B 5 \"audioConstraints|audioBitsPerSecond|sampleRate\" \"/Users/<USER>/Documents/chhlat-bot/src/app/dashboard/knowledge/page.tsx\")", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -A 15 -B 2 \"stopPlayback\" \"/Users/<USER>/Documents/chhlat-bot/src/app/dashboard/knowledge/page.tsx\")", "Bash(npx eslint:*)", "Bash(npm install:*)", "Bash(npm run build:*)", "Bash(npm run lint:*)", "Bash(timeout 30s npm run dev)", "Bash(find:*)", "mcp__ide__getDiagnostics", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n \"playbackTime|introAudioDuration|audioDuration\" \"/Users/<USER>/Documents/chhlat-bot/src/app/dashboard/knowledge/intro/page.tsx\")", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n \"formatTime|Math\\.floor|Math\\.round|toFixed|parseInt.*time|seconds|minutes\" \"/Users/<USER>/Documents/chhlat-bot/src/app/dashboard/knowledge/intro/page.tsx\")", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n -C 3 \"formatRecordingTime|recordingTime|\\{.*Time\\}\" \"/Users/<USER>/Documents/chhlat-bot/src/app/dashboard/knowledge/intro/page.tsx\")", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n -C 2 \"progress|playbackProgress\" \"/Users/<USER>/Documents/chhlat-bot/src/app/dashboard/knowledge/intro/page.tsx\")", "<PERSON><PERSON>(sed:*)", "Bash(rm:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(curl:*)", "Bash(lsof:*)", "Bash(ls:*)", "<PERSON><PERSON>(mv:*)"], "deny": []}}